'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Plus, Search, Edit, Trash2, Eye, Building2, Mail, Phone } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import {
  DataTable,
  type TableColumn,
  type TableActionItem,
} from '@/components/table-component/data-table';
import { CreateEmployerModal } from './components';
import { useEmployers, type Employer, type EmployerStatus } from './api/useEmployers';

export function EmployerModule() {
  const router = useRouter();
  const [searchTerm, setSearchTerm] = useState('');
  const [debouncedSearchTerm, setDebouncedSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<EmployerStatus | 'all'>('all');
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);

  // Debounce search term to avoid too many API calls
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearchTerm(searchTerm);
    }, 300);

    return () => clearTimeout(timer);
  }, [searchTerm]);

  // Fetch employers using the API hook
  const {
    data: employersData,
    isLoading,
    error,
  } = useEmployers({
    page: currentPage,
    limit: itemsPerPage,
    search: debouncedSearchTerm,
    status: statusFilter,
  });

  const employers = employersData?.data || [];
  const totalItems = employersData?.meta.total || 0;
  const totalPages = employersData?.meta.lastPage || 1;

  // Reset pagination when filters change
  const handleFilterChange = () => {
    setCurrentPage(1);
  };

  const handleEmployerCreated = () => {
    // The query will be invalidated by the modal, so no need to do anything here
  };

  const handleDeleteEmployer = (employer: Employer) => {
    if (confirm('Are you sure you want to delete this employer?')) {
      // TODO: Implement delete API
      console.log('Delete employer:', employer.id);
    }
  };

  const handleViewEmployer = (employer: Employer) => {
    router.push(`/admin/employers/${employer.id}`);
  };

  const getStatusBadge = (status: EmployerStatus) => {
    switch (status) {
      case 'ACTIVE':
        return <Badge className="bg-green-100 text-green-800 hover:bg-green-100">Active</Badge>;
      case 'SUSPENDED':
        return <Badge className="bg-red-100 text-red-800 hover:bg-red-100">Suspended</Badge>;
      case 'PENDING':
        return <Badge className="bg-yellow-100 text-yellow-800 hover:bg-yellow-100">Pending</Badge>;
      case 'REJECTED':
        return <Badge className="bg-gray-100 text-gray-800 hover:bg-gray-100">Rejected</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  // Define table columns
  const columns: TableColumn<Employer>[] = [
    {
      key: 'company',
      header: 'Company',
      render: (employer) => (
        <div className="flex items-center space-x-3">
          <Avatar className="h-10 w-10">
            <AvatarImage src={`/placeholder.svg?height=40&width=40`} />
            <AvatarFallback className="bg-[#F74464]/10 font-medium text-[#F74464]">
              {employer.name
                .split(' ')
                .map((n) => n[0])
                .join('')
                .slice(0, 2)}
            </AvatarFallback>
          </Avatar>
          <div>
            <div className="font-medium text-gray-900">{employer.name}</div>
            <div className="flex items-center gap-1 text-sm text-gray-600">
              <Mail className="h-3 w-3" />
              {employer.contactPerson.email}
            </div>
          </div>
        </div>
      ),
    },
    {
      key: 'contactPerson',
      header: 'Contact Person',
      render: (employer) => (
        <div>
          <div className="font-medium text-gray-900">
            {employer.contactPerson.firstName} {employer.contactPerson.lastName}
          </div>
          <div className="flex items-center gap-1 text-sm text-gray-600">
            <Phone className="h-3 w-3" />
            {employer.phoneNumber}
          </div>
        </div>
      ),
    },
    {
      key: 'address',
      header: 'Address',
      render: (employer) => (
        <div className="max-w-xs truncate text-gray-600">{employer.address}</div>
      ),
    },
    {
      key: 'status',
      header: 'Status',
      render: (employer) => getStatusBadge(employer.status),
    },
    {
      key: 'createdAt',
      header: 'Created',
      render: (employer) => <span className="text-gray-600">{formatDate(employer.createdAt)}</span>,
    },
  ];

  // Define table actions
  const actions: TableActionItem<Employer>[] = [
    {
      label: 'View Details',
      icon: <Eye className="h-4 w-4" />,
      onClick: handleViewEmployer,
    },
    {
      label: 'Edit Employer',
      icon: <Edit className="h-4 w-4" />,
      onClick: (employer) => {
        // TODO: Implement edit functionality
        console.log('Edit employer:', employer.id);
      },
      separator: true,
    },
    {
      label: 'Delete Employer',
      icon: <Trash2 className="h-4 w-4" />,
      onClick: handleDeleteEmployer,
      className: 'text-red-600',
    },
  ];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-3xl font-bold tracking-tight text-gray-900">Employers</h2>
          <p className="text-muted-foreground text-lg">Manage employer accounts and information</p>
        </div>
        <Button
          onClick={() => setIsCreateModalOpen(true)}
          className="bg-[#F74464] hover:bg-[#F74464]/90"
        >
          <Plus className="mr-2 h-4 w-4" />
          Add Employer
        </Button>
      </div>

      {/* Stats Cards */}
      {/* <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-[#F74464]/10">
                <Building2 className="h-6 w-6 text-[#F74464]" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Total Employers</p>
                <p className="text-2xl font-bold text-gray-900">{employers.length}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-green-100">
                <Building2 className="h-6 w-6 text-green-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Active Employers</p>
                <p className="text-2xl font-bold text-gray-900">
                  {employers.filter((emp) => emp.status === 'active').length}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-blue-100">
                <Users className="h-6 w-6 text-blue-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Total Employees</p>
                <p className="text-2xl font-bold text-gray-900">
                  {employers.reduce((sum, emp) => sum + emp.employeeCount, 0)}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-purple-100">
                <Building2 className="h-6 w-6 text-purple-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Industries</p>
                <p className="text-2xl font-bold text-gray-900">
                  {new Set(employers.map((emp) => emp.industry)).size}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div> */}

      {/* Employers List */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>All Employers</CardTitle>
              <CardDescription>A list of all registered employers in the system</CardDescription>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          {/* Search and Filters */}
          <div className="mb-6 flex items-center space-x-4">
            <div className="relative max-w-sm flex-1">
              <Search className="text-muted-foreground absolute top-1/2 left-3 h-4 w-4 -translate-y-1/2" />
              <Input
                placeholder="Search employers..."
                value={searchTerm}
                onChange={(e) => {
                  setSearchTerm(e.target.value);
                  handleFilterChange();
                }}
                className="pl-10"
              />
            </div>
            <Select
              value={statusFilter}
              onValueChange={(value: EmployerStatus | 'all') => {
                setStatusFilter(value);
                handleFilterChange();
              }}
            >
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Filter by status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Status</SelectItem>
                <SelectItem value="PENDING">Pending</SelectItem>
                <SelectItem value="ACTIVE">Active</SelectItem>
                <SelectItem value="SUSPENDED">Suspended</SelectItem>
                <SelectItem value="REJECTED">Rejected</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Data Table */}
          <DataTable
            columns={columns}
            data={employers}
            meta={{
              total: totalItems,
              lastPage: totalPages,
              currentPage: currentPage,
            }}
            onPageChange={setCurrentPage}
            itemsPerPage={itemsPerPage}
            onItemsPerPageChange={setItemsPerPage}
            actions={actions}
            isLoading={isLoading}
            error={error?.message || null}
            emptyState={{
              icon: <Building2 className="h-12 w-12 text-gray-400" />,
              title: 'No employers found',
              description: 'No employers match your search criteria.',
            }}
            getRowKey={(employer) => employer.id}
            onRowClick={handleViewEmployer}
          />
        </CardContent>
      </Card>

      {/* Create Employer Modal */}
      <CreateEmployerModal
        isOpen={isCreateModalOpen}
        onClose={() => setIsCreateModalOpen(false)}
        onEmployerCreated={handleEmployerCreated}
      />
    </div>
  );
}
