'use client';

import { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { useQueryClient } from '@tanstack/react-query';
import { Loader2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { toast } from '@/lib/toast';
import { useCreateEmployer } from '../api/useCreateEmployer';
import { useUpdateEmployer } from '../api/useUpdateEmployer';
import { Employer } from '../api/useEmployers';

const employerSchema = z.object({
  companyName: z.string().min(1, 'Company name is required'),
  contactPersonFirstName: z.string().min(1, 'First name is required'),
  contactPersonLastName: z.string().min(1, 'Last name is required'),
  email: z.email('Please enter a valid email address'),
  phoneNumber: z.string().min(1, 'Phone number is required'),
  address: z.string().min(1, 'Address is required'),
});

type EmployerFormData = z.infer<typeof employerSchema>;

interface CreateUpdateEmployerModalProps {
  isOpen: boolean;
  onClose: () => void;
  mode: 'create' | 'edit';
  employerData?: Employer;
  onEmployerCreated?: () => void;
}

export function CreateUpdateEmployerModal({
  isOpen,
  onClose,
  mode,
  employerData,
  onEmployerCreated,
}: CreateUpdateEmployerModalProps) {
  const queryClient = useQueryClient();
  const [isLoading, setIsLoading] = useState(false);
  const { mutateAsync: createEmployer } = useCreateEmployer();
  const { mutateAsync: updateEmployer } = useUpdateEmployer();

  const form = useForm<EmployerFormData>({
    resolver: zodResolver(employerSchema),
    defaultValues: {
      companyName: '',
      contactPersonFirstName: '',
      contactPersonLastName: '',
      email: '',
      phoneNumber: '',
      address: '',
    },
  });

  // Effect to populate form when editing
  useEffect(() => {
    if (mode === 'edit' && employerData) {
      form.reset({
        companyName: employerData.name,
        contactPersonFirstName: employerData.contactPerson.firstName,
        contactPersonLastName: employerData.contactPerson.lastName,
        email: employerData.contactPerson.email,
        phoneNumber: employerData.phoneNumber,
        address: employerData.address,
      });
    } else if (mode === 'create') {
      form.reset({
        companyName: '',
        contactPersonFirstName: '',
        contactPersonLastName: '',
        email: '',
        phoneNumber: '',
        address: '',
      });
    }
  }, [mode, employerData, form]);

  const onSubmit = async (data: EmployerFormData) => {
    try {
      setIsLoading(true);

      if (mode === 'create') {
        await createEmployer(data);
        toast.success('Employer created successfully');
      } else {
        await updateEmployer({ id: employerData!.id, ...data });
        toast.success('Employer updated successfully');
      }

      // Invalidate and refetch employers list
      queryClient.invalidateQueries({ queryKey: ['employers-list'] });

      // If editing, also invalidate the specific employer details
      if (mode === 'edit' && employerData) {
        queryClient.invalidateQueries({ queryKey: ['employer-details', employerData.id] });
      }

      form.reset();
      onClose();
      onEmployerCreated?.();
    } catch (error: any) {
      console.error(`Error ${mode === 'create' ? 'creating' : 'updating'} employer:`, error);
      toast.error(
        error?.response?.data?.message ||
          `Failed to ${mode === 'create' ? 'create' : 'update'} employer`
      );
    } finally {
      setIsLoading(false);
    }
  };

  const handleClose = () => {
    form.reset();
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle>{mode === 'create' ? 'Add New Employer' : 'Edit Employer'}</DialogTitle>
          <DialogDescription>
            {mode === 'create'
              ? 'Create a new employer account with company information.'
              : 'Update the employer information below.'}
          </DialogDescription>
        </DialogHeader>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <FormField
              control={form.control}
              name="companyName"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Company Name</FormLabel>
                  <FormControl>
                    <Input placeholder="Enter company name" {...field} disabled={isLoading} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <div className="grid grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="contactPersonFirstName"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>First Name</FormLabel>
                    <FormControl>
                      <Input placeholder="First name" {...field} disabled={isLoading} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="contactPersonLastName"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Last Name</FormLabel>
                    <FormControl>
                      <Input placeholder="Last name" {...field} disabled={isLoading} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
            <div className="grid grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="email"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Email</FormLabel>
                    <FormControl>
                      <Input
                        type="email"
                        placeholder="Enter email address"
                        {...field}
                        disabled={isLoading}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="phoneNumber"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Phone Number</FormLabel>
                    <FormControl>
                      <Input placeholder="Enter phone number" {...field} disabled={isLoading} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
            <FormField
              control={form.control}
              name="address"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Address</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Enter company address"
                      rows={3}
                      {...field}
                      disabled={isLoading}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <div className="flex justify-end space-x-2 pt-4">
              <Button type="button" variant="outline" onClick={handleClose} disabled={isLoading}>
                Cancel
              </Button>
              <Button
                type="submit"
                className="bg-[#F74464] hover:bg-[#F74464]/90"
                disabled={isLoading}
              >
                {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                {isLoading
                  ? mode === 'create'
                    ? 'Creating...'
                    : 'Updating...'
                  : mode === 'create'
                    ? 'Create Employer'
                    : 'Update Employer'}
              </Button>
            </div>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
