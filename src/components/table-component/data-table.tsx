'use client';

import { ReactNode } from 'react';
import { Chevron<PERSON>eft, ChevronRight, Loader2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { TableActions, type TableActionItem } from '@/components/table-component/table-actions';

// Column definition interface
export interface TableColumn<T = any> {
  key: string;
  header: string;
  render?: (item: T) => ReactNode;
  className?: string;
}

// Re-export TableActionItem for convenience
export type { TableActionItem };

// Meta information for pagination
export interface TableMeta {
  total: number;
  lastPage: number;
  currentPage: number;
}

// Main props interface
export interface DataTableProps<T = any> {
  // Table configuration
  columns: TableColumn<T>[];
  data: T[];

  // Pagination
  meta: TableMeta;
  onPageChange: (page: number) => void;
  itemsPerPage: number;
  onItemsPerPageChange: (itemsPerPage: number) => void;
  itemsPerPageOptions?: number[];

  // Actions
  actions?: TableActionItem<T>[];

  // States
  isLoading?: boolean;
  error?: string | null;

  // Empty state
  emptyState?: {
    icon?: ReactNode;
    title: string;
    description: string;
  };

  // Row configuration
  getRowKey: (item: T) => string | number;
  onRowClick?: (item: T) => void;

  // Additional customization
  className?: string;
  headerClassName?: string;
}

export function DataTable<T = any>({
  columns,
  data,
  meta,
  onPageChange,
  itemsPerPage,
  onItemsPerPageChange,
  itemsPerPageOptions = [5, 10, 20, 50],
  actions = [],
  isLoading = false,
  error = null,
  emptyState,
  getRowKey,
  onRowClick,
  className = '',
  headerClassName = '',
}: DataTableProps<T>) {
  const totalItems = meta.total;
  const totalPages = meta.lastPage;
  const currentPage = meta.currentPage;

  const renderPaginationButtons = () => {
    const pages = Array.from({ length: totalPages }, (_, i) => i + 1).filter((page) => {
      return (
        page === 1 || page === totalPages || (page >= currentPage - 1 && page <= currentPage + 1)
      );
    });

    return pages.map((page, index, array) => (
      <div key={page} className="flex items-center">
        {index > 0 && array[index - 1] !== page - 1 && (
          <span className="text-muted-foreground px-2 text-sm">...</span>
        )}
        <Button
          variant={currentPage === page ? 'default' : 'outline'}
          size="sm"
          onClick={() => onPageChange(page)}
          className={currentPage === page ? 'bg-primary hover:bg-primary/90' : ''}
        >
          {page}
        </Button>
      </div>
    ));
  };

  const renderActions = (item: T) => {
    if (!actions.length) return null;

    return <TableActions item={item} actions={actions} />;
  };

  return (
    <div className={className}>
      {/* Table */}
      <div className="overflow-hidden rounded-lg border border-gray-200">
        <Table>
          <TableHeader>
            <TableRow className={`bg-gray-50 ${headerClassName}`}>
              {columns.map((column) => (
                <TableHead
                  key={column.key}
                  className={`font-semibold text-gray-900 ${column.className || ''}`}
                >
                  {column.header}
                </TableHead>
              ))}
              {actions.length > 0 && (
                <TableHead className="text-right font-semibold text-gray-900">Actions</TableHead>
              )}
            </TableRow>
          </TableHeader>
          <TableBody>
            {isLoading ? (
              <TableRow>
                <TableCell
                  colSpan={columns.length + (actions.length > 0 ? 1 : 0)}
                  className="py-12 text-center"
                >
                  <div className="flex items-center justify-center gap-2">
                    <Loader2 className="h-4 w-4 animate-spin" />
                    <span>Loading...</span>
                  </div>
                </TableCell>
              </TableRow>
            ) : error ? (
              <TableRow>
                <TableCell
                  colSpan={columns.length + (actions.length > 0 ? 1 : 0)}
                  className="py-12 text-center"
                >
                  <div className="text-red-600">{error}</div>
                </TableCell>
              </TableRow>
            ) : data.length === 0 ? (
              <TableRow>
                <TableCell
                  colSpan={columns.length + (actions.length > 0 ? 1 : 0)}
                  className="py-12 text-center"
                >
                  <div className="flex flex-col items-center gap-2">
                    {emptyState?.icon}
                    <p className="text-lg font-medium text-gray-900">
                      {emptyState?.title || 'No data found'}
                    </p>
                    <p className="text-sm text-gray-600">
                      {emptyState?.description || 'No data available.'}
                    </p>
                  </div>
                </TableCell>
              </TableRow>
            ) : (
              data.map((item) => (
                <TableRow
                  key={getRowKey(item)}
                  className={`hover:bg-gray-50 ${onRowClick ? 'cursor-pointer' : ''}`}
                  onClick={() => onRowClick?.(item)}
                >
                  {columns.map((column) => (
                    <TableCell key={column.key} className="py-4">
                      {column.render ? column.render(item) : (item as any)[column.key]}
                    </TableCell>
                  ))}
                  {actions.length > 0 && (
                    <TableCell className="py-4 text-right">{renderActions(item)}</TableCell>
                  )}
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </div>

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="flex items-center justify-between px-2 py-4">
          <div className="flex items-center space-x-2">
            <p className="text-sm font-medium text-gray-700">
              Showing {(currentPage - 1) * itemsPerPage + 1} to{' '}
              {Math.min(currentPage * itemsPerPage, totalItems)} of {totalItems} entries
            </p>
          </div>
          <div className="flex items-center space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => onPageChange(currentPage - 1)}
              disabled={currentPage === 1}
            >
              <ChevronLeft className="h-4 w-4" />
              Previous
            </Button>

            <div className="flex items-center space-x-1">{renderPaginationButtons()}</div>

            <Button
              variant="outline"
              size="sm"
              onClick={() => onPageChange(currentPage + 1)}
              disabled={currentPage === totalPages}
            >
              Next
              <ChevronRight className="h-4 w-4" />
            </Button>
          </div>
        </div>
      )}
    </div>
  );
}
