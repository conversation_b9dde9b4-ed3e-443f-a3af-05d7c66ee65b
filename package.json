{"name": "finwage", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "prepare": "husky"}, "dependencies": {"@hookform/resolvers": "^5.1.1", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-tooltip": "^1.2.7", "@tanstack/react-query": "^5.83.0", "axios": "^1.10.0", "class-variance-authority": "latest", "clsx": "^2.1.1", "date-fns": "^4.1.0", "geist": "^1.4.2", "lucide-react": "latest", "next": "15.4.1", "next-themes": "^0.4.6", "nextjs-toploader": "^3.8.16", "prettier-plugin-tailwindcss": "^0.6.14", "react": "19.1.0", "react-day-picker": "^9.8.0", "react-dom": "19.1.0", "react-hook-form": "^7.60.0", "recharts": "2.15.4", "sonner": "^2.0.6", "tailwind-merge": "^3.3.1", "zod": "^4.0.5", "zustand": "^5.0.6"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "latest", "eslint": "^9", "eslint-config-next": "15.4.1", "eslint-config-prettier": "^10.1.5", "husky": "^9.1.7", "lint-staged": "^16.1.2", "prettier": "3.6.2", "tailwindcss": "^4", "tw-animate-css": "^1.3.5", "typescript": "^5"}, "lint-staged": {"**/*": "prettier --write --ignore-unknown"}}